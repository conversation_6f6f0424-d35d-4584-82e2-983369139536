use walkers::{HttpTiles, MapMemory};

// Temporary simple auth state until we fix the auth module
#[derive(Debug, Clone, serde::Deserialize, serde::Serialize)]
pub struct AuthState {
    pub is_authenticated: bool,
    pub username: Option<String>,
    pub email: Option<String>,
    pub token: Option<String>,
}

impl Default for AuthState {
    fn default() -> Self {
        Self {
            is_authenticated: false,
            username: None,
            email: None,
            token: None,
        }
    }
}

/// Shared data that all tabs can access and modify
#[derive(serde::Deserialize, serde::Serialize)]
#[serde(default)]
pub struct SharedData {
    pub counter: i32,
    pub text: String,
    pub slider_value: f32,
    pub checkbox_state: bool,
    pub items: Vec<String>,

    // Authentication state
    pub auth_state: AuthState,

    // Map-related shared state
    #[serde(skip)]
    pub tiles: Option<HttpTiles>,
    #[serde(skip)]
    pub map_memory: MapMemory,

    // TODO: Add auth manager back when auth module is fixed
}

impl Default for SharedData {
    fn default() -> Self {
        Self {
            counter: 0,
            text: "Shared text across tabs".to_owned(),
            slider_value: 5.0,
            checkbox_state: false,
            items: vec!["Item 1".to_owned(), "Item 2".to_owned()],
            auth_state: AuthState::default(),
            tiles: None,
            map_memory: MapMemory::default(),
            // TODO: Add auth manager back when auth module is fixed
        }
    }
}

impl SharedData {
    pub fn new() -> Self {
        Self::default()
    }

    /// Update authentication state (simplified for now)
    pub fn update_auth_state(&mut self) {
        // TODO: Implement when auth module is fixed
    }

    /// Start native login process (simplified for now)
    #[cfg(not(target_arch = "wasm32"))]
    pub fn start_native_login(&mut self) {
        // TODO: Implement when auth module is fixed
    }

    /// Cancel authentication process (simplified for now)
    #[cfg(not(target_arch = "wasm32"))]
    pub fn cancel_authentication(&mut self) {
        // TODO: Implement when auth module is fixed
    }

    /// Check if login is in progress (simplified for now)
    #[cfg(not(target_arch = "wasm32"))]
    pub fn is_login_in_progress(&self) -> bool {
        false // TODO: Implement when auth module is fixed
    }

    /// Prepare authentication manager for validation of stored auth state (simplified for now)
    #[cfg(not(target_arch = "wasm32"))]
    pub fn prepare_auth_validation(&mut self, _stored_auth_state: AuthState) {
        // TODO: Implement when auth module is fixed
    }

    /// Logout user (simplified for now)
    pub fn logout(&mut self) {
        self.auth_state = AuthState::default();
        log::info!("User logged out");
    }
}